"use client";

import "./page.scss";
import VersionToast from "@/components/VersionToast";
import SurveyLink from "@/components/SurveyLink";
import { useAppDispatch, useAppSelector } from "@/lib/store/hooks";
import Chat from "@/components/Chat";
import Box from "@mui/material/Box";
import { PAGE_NAME } from "@/constants/page";

const VERSION_NUMBER: string = "2025/4";
const VERSION_FEATURES: Array<string> = [
  "希望条件を取得する",
  "3大条件(勤務地、年収、職種)でポジション検索",
  "職種は曖昧でも検索可能",
  "3大条件以外の検索条件は今後追加する予定",
  "当バージョンのポジション検索結果はテキストだが、次のバージョンでは画像付きの綺麗なデザインになる予定",
  "ポジション詳細はまだ確認できませんので、ポジションの詳細が見たい、残業が多いですか、など聞いてもAIは回答できません。ポジション詳細は次のバージョンで見れるようになります。",
];

export default function ChatPage() {
  const dispatch = useAppDispatch();
  const versionToastClosed = useAppSelector(
    (state) => state.globalState.versionToastClosed
  );
  const sessionID = useAppSelector((state) => state.websocket.sessionID);

  if (!versionToastClosed) {
    return (
      <VersionToast
        onClose={() => dispatch({ type: "globalState/closeVersionToast" })}
        versionName={VERSION_NUMBER}
        features={VERSION_FEATURES}
      />
    );
  }

  return (
    <div
      style={{
        position: "relative",
        flex: 1,
        minHeight: 0,
        display: "flex",
        flexDirection: "column",
        width: "100%",
        padding: "0px 100px 0px 100px",
      }}
    >
      {sessionID?.length > 0 && (
        <div>
          <SurveyLink sessionId={sessionID} />
        </div>
      )}
      <Box
        sx={{
          flex: 1,
          display: "flex",
          flexDirection: "column",
          minHeight: 0,
          height: "100%",
        }}
      >
        <Chat currentPage={PAGE_NAME.CHAT} />
      </Box>
    </div>
  );
}
