"use client";

import { fetchApiData, readMockData } from "@/utils/fetch";

export async function getPositionData(sessionID: string, positionId: string) {
  if (process.env.NEXT_PUBLIC_MOCK_API === "true") {
    return readMockData(
      "positions",
      positionId,
      "ポジション情報の取得に失敗しました",
    );
  }
  return fetchApiData(
    `positions/${sessionID}/${positionId}`,
    "ポジション情報の取得に失敗しました",
  );
}

export async function getCompanyData(sessionID: string, positionId: string) {
  if (process.env.NEXT_PUBLIC_MOCK_API === "true") {
    return readMockData(
      "companies",
      positionId,
      "企業情報の取得に失敗しました",
    );
  }
  return fetchApiData(
    `companies/${sessionID}/${positionId}`,
    "企業情報の取得に失敗しました",
  );
}

export async function getBusinessData(sessionID: string, positionId: string) {
  if (process.env.NEXT_PUBLIC_MOCK_API === "true") {
    return readMockData(
      "businesses",
      positionId,
      "事業情報の取得に失敗しました",
    );
  }
  return fetchApiData(
    `businesses/${sessionID}/${positionId}`,
    "事業情報の取得に失敗しました",
  );
}
