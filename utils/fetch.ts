export async function readMockData(
  path: string,
  id: string,
  errorMsg: string,
) {
  try {
    const response = await fetch(
      `/mock/api/responses/${path}/${id}.json`,
    );
    if (!response.ok) throw new Error(`Failed to fetch mock ${path} data`);
    const data = await response.json();
    return { data, error: null };
  } catch (err) {
    console.error(`Error loading mock ${path} data:`, err);
    return { data: null, error: errorMsg };
  }
}

export async function fetchApiData(
  path: string,
  errorMsg: string,
  data?: any,
) {
  const apiEndpoint = process.env.NEXT_PUBLIC_API_ENDPOINT;
  const url = `${apiEndpoint}/${path}`;
  try {
    const fetchOptions: RequestInit = data
      ? {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify(data),
        }
      : {};
    const response = await fetch(url, fetchOptions);
    if (!response.ok) throw new Error(`Failed to fetch ${path} data`);
    const result = await response.json();
    return { data: result, error: null };
  } catch (err) {
    console.error(err);
    return { data: null, error: errorMsg };
  }
}