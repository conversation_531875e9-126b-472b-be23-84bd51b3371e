## キャリアエージェントのフロントエンド

`.env.example`を`.env.local`に変えて、

NEXT_PUBLIC_AGENT_ENDPOINTにローカルか検証サーバのエンドポイントに定義して、

`npm run dev`で起動できます。

## ブランチ

ブランチ名は「自分の名前」/「機能名」で開発してください（例：flavioaraki/login）。

開発環境へデプロイするには、自分の開発ブランチを「develop」ブランチにマージしてください。
　GitHub Actionで「build-dev」ブランチにビルドされた静的ファイルが保存されます。

本番環境へデプロイするには、developブランチを「master」ブランチにマージしてください。
　GitHub Actionで「build-prod」ブランチにビルドされた静的ファイルが保存されます。

## 開発手順

1. `npm run dev`で開発サーバーを起動
2. ソースコード更新
3. `npm run build`でビルド
4. TypeScriptエラーが発生したら修正する

## 配布用の静的ファイル作成

### 開発環境

```
cp .env.dev.example .env.production
npm run build
```

## ポジション詳細画面について

[本体](https://github.com/MIIDAS-Company/miidas_user_next/blob/develop/src/components/positions/PositionDetail.jsx)からポジション詳細に必要なコンポーネントを持ってきて、表示できるようになりました。

### 残課題・対応

#### websocketの管理

websocketは、いま/chatページで管理していて、mount時に接続して、unmount時に切断しています。

なので、/chat→/positionsに遷移する際に、websocketは切断されてしまいます。

また、全体的に1つの会話なので、websocket管理を外だしする必要があります。
Reduxの利用を考えています。

### ページレイアウト確認

外部公開ポジションIDと本当のポジションIDとのマッピングはエージェントサーバのwebsocketセッション毎にメモリに保存しています。

また、websocketが切断する際に、エージェントサーバがそのセッションを削除しますので、そのマッピングはもうなくなります。

そのため、ポジション詳細画面に遷移する際に、websocketが切断され、サーバ側のマッピングはなくなるので、
本当のポジションIDが取れなくなるため、ポジション詳細データが取得できなくなります。

今対応中なので、APIモックデータを使って詳細画面を確認します。

#### 環境変数設定

`NEXT_PUBLIC_MOCK_API=true`追加

#### モックデータ

public/mock/api/responses

position、companies、businessesにそれぞれjsonファイルを用意する必要があり、
ファイル名はposition IDとなります。

#### アクセス方法

http://localhost:3000/positions?positionId=ポジションID
