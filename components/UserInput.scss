.user-input-footer {
  width: 100%;
  display: flex;
  justify-content: space-around;
  align-items: center;
}

.user-input-container {
  $inactive: var(--body-background);
  $active: white;
  border: 1px solid var(--mui-palette-primary-main);
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  padding: 0.5rem 1rem;
  justify-content: space-between;
  align-items: center;
  border-radius: 100px;
  border: 1px solid var(--bg-primary, #7fafe2);
  background: var(--body-background);

  &:hover {
    border-color: var(--mui-palette-primary-light);
  }

  &.active {
    background-color: $active;
  }

  &.inactive {
    background-color: $inactive;
  }

  textarea {
    border-radius: 100px;
    resize: none;
    line-height: 200%;
    padding: 1rem 2rem;
    width: 100%;
    border: none;
    background-color: inherit;

    &:focus {
      border: none;
      outline: none;
    }
  }

  /* PCのチャット一覧の最大幅は1000px */
  @media screen and (min-width: 1000px) {
    max-width: 1000px;
  }
}
