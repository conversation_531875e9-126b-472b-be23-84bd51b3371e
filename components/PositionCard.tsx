import * as React from "react";
import Card from "@mui/material/Card";
import CardActions from "@mui/material/CardActions";
import CardContent from "@mui/material/CardContent";
import CardMedia from "@mui/material/CardMedia";
import Button from "@mui/material/Button";
import Typography from "@mui/material/Typography";
import { PositionSummary } from "@/lib/common";
import { Box } from "@mui/material";
import { useRouter } from "next/navigation";

interface PositionCardProps {
  positions: PositionSummary[];
}

export default function PositionCard({ positions }: PositionCardProps) {
  const router = useRouter();
  return (
    <Box sx={{ display: "flex", flexDirection: "column", gap: 2 }}>
      {positions.map((position, index) => (
        <Card key={index} sx={{ maxWidth: 600 }}>
          {position.Image && (
            <CardMedia
              component="img"
              alt="green iguana"
              height="140"
              image={position.Image}
            />
          )}
          <CardContent>
            <Typography gutterBottom variant="h5" component="div">
              {position.Title}
            </Typography>
            <Typography variant="body2" color="text.secondary" component="pre">
              {position.MainJobText}
            </Typography>
          </CardContent>
          <CardActions sx={{ justifyContent: "flex-end" }}>
            <Button
              size="small"
              onClick={() =>
                router.push(`/positions/?positionId=${position.ID}`)
              }
            >
              詳細を見る
            </Button>
          </CardActions>
        </Card>
      ))}
    </Box>
  );
}
