"use client";

import "./Chat.scss";
import { useCallback, useEffect, useRef, useState } from "react";
import { ChatMessage } from "@/components/ChatMessage";
import RecommendationList from "@/components/positions/recommendations/RecommendationList";
import UserInput from "@/components/UserInput";
import PositionCard from "@/components/PositionCard";
import { useAppDispatch, useAppSelector } from "@/lib/store/hooks";
import Box from "@mui/material/Box";
import { PAGE_NAME } from "@/constants/page";
import { useRouter } from "next/navigation";
import { Item } from "@/lib/common";
import { ChatMessageRole, SocketStatus } from "@/constants/enum";
import { sendWebSocketMessage } from "@/lib/socket";

// 一旦外す
// const MESSAGE_RESPONSE_TIMEOUT: number = 60000; // 60秒

export interface IChatProps {
  currentPage: string;
  positionID?: string | null;
}

export default function Chat(props: IChatProps) {
  const [dots, setDots] = useState<string>("");
  const bottomRef = useRef<HTMLDivElement>(null);
  // const messageTimeoutRef = useRef<
  //   string | number | NodeJS.Timeout | undefined
  // >(undefined);
  const intervalIdRef = useRef<string | number | NodeJS.Timeout | undefined>(
    undefined
  );
  const pageInitializedRef = useRef<string>("");

  const router = useRouter();
  const dispatch = useAppDispatch();
  const socketStatus = useAppSelector((state) => state.websocket.status);
  const isConnected = useAppSelector(
    (state) => state.websocket.status >= SocketStatus.Connected
  );
  const previousPage = useAppSelector((state) => state.websocket.currentPage);
  const allItems = useAppSelector((state) => state.websocket.items);

  const send = useCallback(
    (message: string) => {
      sendWebSocketMessage(
        message,
        previousPage,
        props.currentPage,
        props.positionID
      );
    },
    [previousPage, props]
  );

  const getItems = useCallback((): Item[] => {
    if (props.currentPage == PAGE_NAME.CHAT) {
      // メインチャート
      return allItems;
    } else if (props.positionID) {
      // ポジション詳細チャート
      for (const item of allItems) {
        if (item.positionSearchResult) {
          for (const position of item.positionSearchResult.positions) {
            if (position.ID.toString() === props.positionID) {
              return position.items;
            }
          }
        }
      }
    }

    return [];
  }, [allItems, props]);

  // メインチャートとポジション詳細チャート（ポジションIDあり）が以外の場合、強制的にメインチャートページに遷移する。
  useEffect(() => {
    if (props.currentPage == PAGE_NAME.CHAT) {
      // メインチャート
    } else if (
      props.currentPage == PAGE_NAME.POSITION_DETAIL &&
      props.positionID
    ) {
      // ポジション詳細チャート
    } else {
      // should not happen
      router.replace("/chat");
    }
  }, [router, allItems, props]);

  // ページ初期化
  useEffect(() => {
    if (!isConnected) {
      return;
    }

    console.debug(`init when page ${props.currentPage} is showing`);

    // currently `currentPage` is actually previous page
    console.debug("previousPage", previousPage);
    console.debug("props.currentPage", props.currentPage);
    console.debug("sessionStorage", sessionStorage.getItem("currentPage"));

    // Create a unique key for this page initialization to prevent duplicate sends
    const pageKey = `${props.currentPage}_${props.positionID || 'main'}`;

    // Check if we've already initialized this page in this session
    if (pageInitializedRef.current === pageKey) {
      console.debug("Page already initialized, skipping send");
      return;
    }

    // In development mode `useEffect` will always run at lease twice and Redux state is not preserved yet,
    // so put current page in sessionStorage to prevent below logic running twice
    if (sessionStorage.getItem("currentPage") !== props.currentPage) {
      if (props.currentPage !== previousPage) {
        if (props.currentPage === PAGE_NAME.CHAT) {
          if (previousPage === "") {
            // first access or reload `/chat`
            send("こんにちは");
          } else if (previousPage === PAGE_NAME.POSITION_DETAIL) {
            // go back from position detail
            send("###PositionAdvisorEnd###");
          }
        } else if (props.currentPage === PAGE_NAME.POSITION_DETAIL) {
          if (props.positionID) {
            // TODO: ユーザーが初めてメッセージを送った際に、一緒に送るように変更
            send(`###PositionAdvisorStart###`);
          } else {
            // should not happen
            router.replace("/chat");
          }
        }

        // Mark this page as initialized
        pageInitializedRef.current = pageKey;

        // update `currentPage` to current page.
        dispatch({
          type: "websocket/setCurrentPage",
          payload: props.currentPage,
        });
      }

      // save current page to session storage
      sessionStorage.setItem("currentPage", props.currentPage);
    }
  }, [router, dispatch, previousPage, props, isConnected, send]);

  // Reset the page initialization ref when the page actually changes
  useEffect(() => {
    const pageKey = `${props.currentPage}_${props.positionID || 'main'}`;
    if (pageInitializedRef.current && pageInitializedRef.current !== pageKey) {
      pageInitializedRef.current = "";
    }
  }, [props.currentPage, props.positionID]);

  // 画面表示項目（メッセージ）に変更があった場合、いつもページの最後に移動する。
  // TODO: この動きは良い？
  // 特にポジション検索結果が受信されたとき、一番目のポジション結果が見れるようにするのは正しそう
  // また、ポジション詳細画面から戻ってきたときに、メインチャートは受信がありますが、いま見たポジション詳細の位置で良いかもしれない。
  useEffect(() => {
    // const timeout = setTimeout(() => {
    bottomRef.current?.scrollIntoView({ behavior: "smooth" });
    // }, 500);

    // return () => clearTimeout(timeout);
  }, [allItems, dots]);

  // websocketステータス変わったときの対応
  useEffect(() => {
    if (!isConnected || socketStatus == SocketStatus.MessageSent) {
      // show waiting sign
      if (!intervalIdRef.current) {
        intervalIdRef.current = setInterval(() => {
          setDots((prev) => {
            if (prev === "...") return ".";
            else return prev + ".";
          });
        }, 100);
      }

      // after user sent message
      // if (socketStatus == SocketStatus.MessageSent) {
      //   if (messageTimeoutRef.current) {
      //     clearTimeout(messageTimeoutRef.current);
      //   }
      //   messageTimeoutRef.current = setTimeout(() => {
      //     console.debug("messageTimeoutRef timed out");
      //   }, MESSAGE_RESPONSE_TIMEOUT);
      // }
    } else {
      if (intervalIdRef.current) {
        // clear waiting sign
        clearInterval(intervalIdRef.current);
        intervalIdRef.current = undefined;

        setDots("");
      }

      // if (messageTimeoutRef.current) {
      //   clearTimeout(messageTimeoutRef.current);
      // }
    }
  }, [socketStatus, isConnected]);

  const sendUserInput = (newValue: string) => {
    console.debug("sendUserInput newValue =", newValue);

    dispatch({
      type: "websocket/updateItem",
      payload: {
        newItem: {
          role: ChatMessageRole.User,
          item_id: `input_${Date.now()}`,
          message: newValue,
        },
        positionID: props.positionID,
      },
    });

    send(newValue);
  };

  const displayItem = (previousRole: string | null, item: Item) => {
    if (item.positionSearchResult) {
      const { positions, recommendations, search_key } =
        item.positionSearchResult;
      if (positions.length > 0 || recommendations.length > 0) {
        return (
          <>
            {positions.length > 0 && (
              <Box
                className="chat-message-container"
                key={item.item_id + "_positions"}
              >
                <PositionCard positions={positions} />
              </Box>
            )}
            {recommendations.length > 0 && (
              <Box
                className="chat-message-container"
                key={item.item_id + "_recommendations"}
              >
                <RecommendationList
                  searchKey={search_key}
                  recommendations={recommendations}
                />
              </Box>
            )}
          </>
        );
      }
    } else {
      return (
        <Box className="chat-message-container" key={item.item_id}>
          <ChatMessage
            showIcon={item.role === previousRole}
            role={item.role as ChatMessageRole}
            message={item.message}
          />
        </Box>
      );
    }
  };

  return (
    <div className="chat-root">
      <div className="chat-messages-list">
        {(() => {
          const items = getItems();
          return items.map((chatMessage, i) => {
            const previousRole = i > 0 ? items[i - 1].role : null;
            return displayItem(previousRole, chatMessage);
          });
        })()}
        {!isConnected && (
          // || (startReceiving && !isReceiving) && (
          <div className="conversation-item typing-indicator">
            <div className="speaker-content">
              {!isConnected && <span>ネット接続中</span>}
              {dots}
              {!isConnected && (
                <p>
                  時間が経過しても接続できない場合、この画面を閉じて、再度開いてみてください
                </p>
              )}
            </div>
          </div>
        )}
        <div ref={bottomRef} />
      </div>
      {isConnected && <UserInput sendCallback={sendUserInput} />}
    </div>
  );
}
