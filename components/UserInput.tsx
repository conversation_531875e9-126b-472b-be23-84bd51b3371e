import "./UserInput.scss";
import { useState, useEffect, useRef, useCallback, useMemo } from "react";
import Box from "@mui/material/Box";
import TextareaAutosize from "@mui/material/TextareaAutosize";
import Snackbar from "@mui/material/Snackbar";
import IconButton from "@mui/material/IconButton";
import SendIcon from "@mui/icons-material/Send";
import MicrophoneIcon from "@mui/icons-material/Mic";
import { SpeechToText, ISpeechToTextParams } from "@/lib/speechToText";
import { removeSpaces } from "@/lib/strings";

// 音声入力モードの無音の閾値
const VOICE_INPUT_SILENCE_THRESHOLD: number = 3000;
const SNACKBAR_DISPLAY_TIME: number = 4000;

type SendHandler = (userInput: string) => void;

export interface IUserInputProps {
  sendCallback: SendHandler;
}

enum InputMode {
  Keyboard,
  Voice,
}

enum ButtonIconMode {
  None,
  MicrophoneOff,
  MicrophoneOn,
  Send,
}

export default function UserInput({ sendCallback }: IUserInputProps) {
  const textareaRef = useRef(null);
  const speechToTextRef = useRef<SpeechToText | null>(null);
  const [inputMode, setInputMode] = useState(InputMode.Keyboard);
  const [isSpeechToTextAvailable, setIsSpeechToTextAvailable] = useState(false);
  const [isSnackbarOpen, setIsSnackbarOpen] = useState(false);
  const [localValue, setLocalValue] = useState("");

  const handleSnackbarClose = () => {
    setIsSnackbarOpen(false);
  };

  useEffect(() => {
    // windowにフォーカスが当たった場合、
    // 入力モードが音声であれば、マイクを再開する
    const handleFocus = (event: FocusEvent) => {
      console.debug("Window gained focus!", event);
      if (inputMode !== InputMode.Voice) {
        return;
      }
      speechToTextRef.current?.startListening();
    };

    const handleBlur = (event: FocusEvent) => {
      console.debug("Window lost focus!", event);
      speechToTextRef.current?.abortListening();
    };

    // Add both event listeners
    window.addEventListener("focus", handleFocus);
    window.addEventListener("blur", handleBlur);

    // Cleanup event listeners on component unmount
    return () => {
      window.removeEventListener("focus", handleFocus);
      window.removeEventListener("blur", handleBlur);
    };
  });

  // 入力されたまたは文字起こしされたテキストを送信する
  const sendUserInput = (userInput: string) => {
    sendCallback(userInput);
    setLocalValue("");
  };

  const handleRecognitionEnd = useCallback(
    (transcription: string) => {
      console.debug("handleRecognitionEnd called");
      if (transcription.length === 0) {
        return;
      }

      // 最後の文字起こしであれば送信する
      sendUserInput(transcription);
      setLocalValue("");
    },
    [sendUserInput],
  );

  // 音声からの文字起こしを処理する
  const handleTranscription = useCallback(
    (transcription: string, isFinal: boolean) => {
      console.debug(
        "handleTranscription transcription =",
        transcription,
        "isFinal?",
        isFinal,
      );
      const newValue = removeSpaces(transcription);

      if (isFinal) {
        // 最後の文字起こしであれば送信する
        handleRecognitionEnd(newValue);
      } else {
        // 最後の文字起こしでなければTextAreaに追加するだけ
        setLocalValue(newValue);
      }
    },
    [handleRecognitionEnd],
  );

  useEffect(() => {
    console.debug("useEffect for instantiating speechToTextRef called");
    if (!SpeechToText.isSpeechRecognitionAvailable()) {
      setInputMode(InputMode.Keyboard);
      return;
    }
    const params: ISpeechToTextParams = {
      transcriptCallback: handleTranscription,
      silenceThreshold: VOICE_INPUT_SILENCE_THRESHOLD,
    };
    try {
      const stt = SpeechToText.getInstance(params);
      // console.debug("after instanciation speechToText.recognition =", stt.recognition)
      speechToTextRef.current = stt;
      setIsSpeechToTextAvailable(true);
    } catch (e: unknown) {
      console.error("error initializing SpeechToText", e);
      setInputMode(InputMode.Keyboard);
    }

    return () => {
      speechToTextRef.current = null;
    };
  }, []);

  const handleClick = useCallback(() => {
    console.debug("handleClick called");
    // 入力テキストの長さが1文字以上であれば送信する
    if (localValue.length > 0) {
      console.debug("going to send userInput");
      sendUserInput(localValue);
      return;
    }

    // 現在の入力モードがキーボードの場合、音声入力モードに切り替える
    if (inputMode === InputMode.Keyboard) {
      if (isSpeechToTextAvailable) {
        console.debug("going to switch to voice mode");
        setInputMode(InputMode.Voice);
        // trueはユーザーが開始したことを意味する
        speechToTextRef.current?.startListening(true);
      } else {
        setIsSnackbarOpen(true);
      }

      // 現在の入力モードが音声入力の場合、キーボード入力モードに切り替える
    } else {
      console.debug("going to switch to keyboard mode");
      setInputMode(InputMode.Keyboard);
      // trueはユーザーが停止したことを意味する
      speechToTextRef.current?.abortListening(true);
    }
  }, [
    inputMode,
    sendUserInput,
    speechToTextRef,
    isSpeechToTextAvailable,
    localValue,
  ]);

  const handleChange = useCallback(
    (e: React.ChangeEvent<HTMLTextAreaElement>) => {
      const newValue = e.target.value;
      setLocalValue(newValue);
    },
    [],
  );

  const handleKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    if (!e.ctrlKey || e.key !== "Enter") {
      return;
    }
    sendUserInput(localValue);
  };

  const buttonIconMode = useMemo(() => {
    if (!isSpeechToTextAvailable) {
      if (localValue.length > 0) {
        return ButtonIconMode.Send;
      }
      return ButtonIconMode.None;
    }

    if (inputMode === InputMode.Voice) {
      return ButtonIconMode.MicrophoneOn;
    }

    if (localValue.length === 0) {
      return ButtonIconMode.MicrophoneOff;
    }

    return ButtonIconMode.Send;
  }, [isSpeechToTextAvailable, inputMode, localValue]);

  return (
    <Box className="user-input-footer">
      <Box
        className={`user-input-container ${localValue.length > 0 ? "active" : "inactive"}`}
      >
        <TextareaAutosize
          ref={textareaRef}
          className={`${localValue.length > 0 ? "active" : "inactive"}`}
          placeholder="ここに入力またはマイクを押してください"
          value={localValue}
          minRows={1}
          maxRows={50}
          onChange={handleChange}
          onKeyDown={handleKeyDown}
        />
        {buttonIconMode !== ButtonIconMode.None && (
          <IconButton onClick={handleClick}>
            {buttonIconMode === ButtonIconMode.MicrophoneOff && (
              <MicrophoneIcon color="primary" />
            )}
            {buttonIconMode === ButtonIconMode.MicrophoneOn && (
              <MicrophoneIcon color="secondary" />
            )}
            {buttonIconMode === ButtonIconMode.Send && (
              <SendIcon color="primary" />
            )}
          </IconButton>
        )}
      </Box>
      <Snackbar
        open={isSnackbarOpen}
        autoHideDuration={SNACKBAR_DISPLAY_TIME}
        onClose={handleSnackbarClose}
        message="メッセージを入力してください"
      />
    </Box>
  );
}
