// 音声から文字起こしができた場合に呼び出される関数
export type TranscriptCallback = (
  transcription: string,
  isFinal: boolean,
) => void;
// 文字起こしが停止したら呼び出される関数
export type RecognitionEndCallback = () => void;

export interface ISpeechToTextParams {
  transcriptCallback: TranscriptCallback;
  silenceThreshold: number;
}

export class SpeechToText {
  private recognition: SpeechRecognition | null = null;
  private isListening: boolean = false;
  private silenceTimer: NodeJS.Timeout | null = null;
  private transcriptCallback: TranscriptCallback;
  private silenceThreshold: number; // SilenceCallbackを呼び出すまでの経過時間(ms)
  private lastTranscript: string = "";
  private stoppedByUser: boolean = false; // ユーザーが停止したフラグ
  private static instance: SpeechToText;

  constructor(params: ISpeechToTextParams) {
    console.debug("SpeechToText constructor called");
    this.transcriptCallback = params.transcriptCallback;
    this.silenceThreshold = params.silenceThreshold;
    this.initializeSpeechRecognition();
  }

  static getInstance(params: ISpeechToTextParams): SpeechToText {
    if (!SpeechToText.instance) {
      SpeechToText.instance = new SpeechToText(params);
    }
    return SpeechToText.instance;
  }

  static isSpeechRecognitionAvailable(): boolean {
    if (
      !window ||
      (!("SpeechRecognition" in window) &&
        !("webkitSpeechRecognition" in window))
    ) {
      return false;
    }
    return true;
  }

  private initializeSpeechRecognition(): void {
    if (!SpeechToText.isSpeechRecognitionAvailable()) {
      throw new Error("Speech recognition is not supported in this browser.");
    }

    const SpeechRecognition =
      window?.SpeechRecognition || window?.webkitSpeechRecognition;
    this.recognition = new SpeechRecognition();
    this.recognition.continuous = true;
    this.recognition.interimResults = true;
    this.recognition.lang = "ja-JP";

    this.recognition.onresult = this.handleRecognitionResult.bind(this);
    this.recognition.onerror = this.handleRecognitionError.bind(this);
    this.recognition.onend = this.handleRecognitionEnd.bind(this);
    this.recognition.onspeechend = this.handleSpeechEnd.bind(this);
  }

  private handleRecognitionResult(event: SpeechRecognitionEvent): void {
    const lastResult = event.results[event.results.length - 1];
    if (lastResult.isFinal) {
      // 文字起こしのisFinalイベントの前に送信しているので、
      // isFinalがきたら送信済みだという意味
      return;
    }
    this.resetSilenceTimer();
    let interimTranscript = "";
    for (let i = event.resultIndex; i < event.results.length; ++i) {
      const result = event.results[i];
      interimTranscript += result[0].transcript;
      this.transcriptCallback(interimTranscript, result.isFinal);
      this.lastTranscript = interimTranscript;
    }
  }

  private handleRecognitionError(event: SpeechRecognitionErrorEvent): void {
    console.debug("SpeechToText handleRecognitionError error:", event.error);
    this.handleRecognitionEnd();
  }

  private handleRecognitionEnd(): void {
    console.debug("SpeechToText handleRecogitionEnd called");
    this.clearSilenceTimer();
    this.isListening = false;
    this.startListening(); // 自動再開
  }

  private handleSpeechEnd(): void {
    console.debug("SpeechToText handleSpeechEnd called");
    this.handleRecognitionEnd();
  }

  private resetSilenceTimer(): void {
    this.clearSilenceTimer();
    this.silenceTimer = setTimeout(() => {
      console.debug("SpeechToText silence timeout. Aborting listening");
      this.abortListening();
      this.transcriptCallback(this.lastTranscript, true);
      this.lastTranscript = "";
      this.startListening(); // 自動再開
    }, this.silenceThreshold);
  }

  private clearSilenceTimer(): void {
    if (!this.silenceTimer) {
      return;
    }
    clearTimeout(this.silenceTimer);
    this.silenceTimer = null;
  }

  public startListening(startedByUser: boolean = false): void {
    console.debug("SpeechToText startListening called");
    if (startedByUser) {
      this.stoppedByUser = false;
    }
    if (this.stoppedByUser) {
      // ユーザーが停止しているなら、
      // ユーザーが再開するまで自動再開しない
      return;
    }
    try {
      this.recognition?.start();
    } catch (e: unknown) {
      console.debug("SpeechToText startListening exception:", e);
    }
    this.isListening = true;
    this.stoppedByUser = false;
  }

  // マイクを停止して文字起こしも同時に停止する
  // 結果、この後に文字起こしイベントは発生しない
  public abortListening(stoppedByUser: boolean = false): void {
    console.debug("SpeechToText abortListening called");
    try {
      this.recognition?.abort();
    } catch (e: unknown) {
      console.debug("SpeechToText abortListening exception:", e);
    }
    if (stoppedByUser) {
      this.stoppedByUser = true;
    }
  }

  public getListening() {
    return this.isListening;
  }
}
