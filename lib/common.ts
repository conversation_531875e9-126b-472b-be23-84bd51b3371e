export interface PositionSummary {
  ID: number;
  Title: string;
  MainJobText: string;
  SalaryFrom: string;
  SalaryTo: string;
  Image: string
  items: Item[];
}

export interface PositionRecommendation {
  Theme: string;
  Title: string;
  Description?: string;
}

export interface PositionSearchResult {
  search_key: string;
  positions: PositionSummary[];
  recommendations: PositionRecommendation[];
}

export interface Item {
  role: string;
  item_id: string;
  message: string;
  positionSearchResult?: PositionSearchResult;
}
