import { createSlice, PayloadAction } from "@reduxjs/toolkit";
import { AppThunk } from "@/lib/store";
import { sessionStorageKey } from "@/constants/session";
import { Item } from "@/lib/common";
import { SocketStatus } from "@/constants/enum";

export interface WebSocketState {
  status: SocketStatus;
  sessionID: string;
  items: Item[];
  currentPage: string;
}

const initialState: WebSocketState = {
  status: SocketStatus.Unknown,
  sessionID: "",
  items: [],
  currentPage: "",
};

const websocketSlice = createSlice({
  name: "websocket",
  initialState,
  reducers: {
    setStatus(state, action: PayloadAction<SocketStatus>) {
      state.status = action.payload;
    },
    setConnected: (state, action: PayloadAction<string | null>) => {
      state.status = SocketStatus.Connected;
      state.sessionID = action.payload ?? "";
    },
    setDisconnected: (state) => {
      state.status = SocketStatus.Disconnected;
      state.sessionID = "";
      state.currentPage = "";
    },
    setSessionID: (state, action: PayloadAction<string>) => {
      state.sessionID = action.payload;
    },
    updateItem: (
      state,
      action: PayloadAction<{ newItem: Item; positionID?: string }>
    ) => {
      // チャートのレスポンスは来る前に、メインチャート<=>ポジション詳細チャートが切り替わった可能性がありますので、
      // 受け取ったレスポンスは必ず今表示中画面のチャートのレスポンスに限らないので、常にレスポンスの内容(positionID)を見て、どのアイテムを更新するかを判断する。
      if (action.payload.positionID) {
        let updatedPositionItems: Item[] = [];

        for (const item of state.items) {
          if (item.positionSearchResult) {
            for (const position of item.positionSearchResult.positions) {
              if (position.ID.toString() === action.payload.positionID) {
                const existingIndex = position.items.findIndex(
                  (item) => item.item_id === action.payload.newItem.item_id
                );

                if (existingIndex !== -1) {
                  updatedPositionItems = position.items.map((item, idx) =>
                    idx === existingIndex
                      ? {
                          ...item,
                          message:
                            item.message + action.payload.newItem.message,
                        }
                      : item
                  );
                } else {
                  updatedPositionItems = [
                    ...position.items,
                    {
                      role: action.payload.newItem.role,
                      item_id: action.payload.newItem.item_id,
                      message: action.payload.newItem.message,
                    },
                  ];
                }

                break;
              }
            }
          }
        }

        const updatedItems = state.items.map((item) => ({
          ...item,
          positionSearchResult: item.positionSearchResult
            ? {
                ...item.positionSearchResult,
                positions: item.positionSearchResult.positions.map((pos) =>
                  pos.ID.toString() === action.payload.positionID
                    ? { ...pos, items: updatedPositionItems }
                    : pos
                ),
              }
            : item.positionSearchResult,
        }));
        state.items = updatedItems;
      } else {
        let updatedItems: Item[] = [];

        const existingIndex = state.items.findIndex(
          (item) => item.item_id === action.payload.newItem.item_id
        );

        if (existingIndex !== -1) {
          updatedItems = state.items.map((item, idx) =>
            idx === existingIndex
              ? {
                  ...item,
                  message: item.message + action.payload.newItem.message,
                }
              : item
          );
        } else {
          updatedItems = [
            ...state.items,
            {
              role: action.payload.newItem.role,
              item_id: action.payload.newItem.item_id,
              message: action.payload.newItem.message,
              positionSearchResult: action.payload.newItem.positionSearchResult,
            },
          ];
        }

        state.items = updatedItems;
      }
    },
    setCurrentPage: (state, action) => {
      state.currentPage = action.payload;
    },
  },
});

export const saveSessionID =
  (sessionID: string): AppThunk =>
  (dispatch) => {
    localStorage.setItem(sessionStorageKey, sessionID);
    dispatch(websocketSlice.actions.setSessionID(sessionID));
  };

export const { setStatus, setConnected, setDisconnected, setSessionID } =
  websocketSlice.actions;
export default websocketSlice.reducer;
