import { Action, configureStore, ThunkAction } from "@reduxjs/toolkit";
import websocketReducer from "./features/websocket/websocketSlice";
import globalStateReducer from "./features/global_state/globalStateSlice";

export const makeStore = () => {
  return configureStore({
    reducer: {
      websocket: websocketReducer,
      globalState: globalStateReducer,
    },
  });
};

// Infer the type of makeStore
export type AppStore = ReturnType<typeof makeStore>;
// Infer the `RootState` and `AppDispatch` types from the store itself
export type RootState = ReturnType<AppStore["getState"]>;
export type AppDispatch = AppStore["dispatch"];

export type AppThunk<ReturnType = void> = ThunkAction<
  ReturnType,
  RootState,
  unknown,
  Action<string>
>;
